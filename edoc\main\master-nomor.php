<?php
session_start();
include '../config.php';
error_reporting(E_ALL ^ (E_NOTICE | E_WARNING));

if(empty($_SESSION['username'])){
    header("location:../login.php");
}

$user = $_SESSION['username'];
$hak_akses = $_SESSION['hak_akses'];

// Hanya admin yang bisa akses
if($hak_akses != '1'){ 
    header("location:index.php");
}

// Fungsi untuk format tanggal Indonesia
function tgl($tanggal){
    if(empty($tanggal) || $tanggal == '0000-00-00') return '-';
    $bulan = array(1 => '<PERSON><PERSON><PERSON>','<PERSON><PERSON><PERSON>','Mare<PERSON>','April','<PERSON>','<PERSON><PERSON>','<PERSON><PERSON>','Agustus','September','Oktober','November','Desember');
    $split = explode('-', $tanggal);
    return $split[2] . ' ' . $bulan[(int)$split[1]] . ' ' . $split[0];
}
?>
<!DOCTYPE html>
<html>
<head>
    <title>Edoc - Master <PERSON></title>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <link rel="stylesheet" type="text/css" href="../assets/css/main.css">
    <link rel="stylesheet" type="text/css" href="../assets/bootstrap/css/bootstrap.min.css">
    <link rel="stylesheet" type="text/css" href="../assets/plugins/datatables/css/dataTables.bootstrap.min.css">
    <link rel="stylesheet" type="text/css" href="../assets/css/sweetalert.css">
    <link rel="shortcut icon" href="../assets/images/favicon.ico" type="image/x-icon">
    <link rel="icon" href="../assets/images/favicon.ico" type="image/x-icon">
    <style>
        .form-section {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            border: 1px solid #dee2e6;
        }
        .level-row {
            margin-bottom: 15px;
            padding: 10px;
            background: white;
            border-radius: 5px;
            border-left: 4px solid #FF6347;
        }
        .btn-add {
            background-color: #FF6347 !important;
            border-color: #FF6347 !important;
            color: white !important;
        }
        .btn-add:hover {
            background-color: #e55347 !important;
            border-color: #e55347 !important;
        }
        .btn-add:disabled {
            background-color: #ccc !important;
            border-color: #ccc !important;
            cursor: not-allowed;
        }
        .table-head {
            background-color: #FF6347 !important;
            color: white !important;
        }
        .preview-kode {
            font-weight: bold;
            color: #FF6347;
            font-size: 14px;
        }
    </style>
</head>
<body class="sidebar-mini fixed">
    <div class="wrapper">
        <!-- Header -->
        <header class="main-header hidden-print">
            <a class="logo" href="index.php" style="font-size:13pt">RSK Dharmais</a>
            <nav class="navbar navbar-static-top">
                <a class="sidebar-toggle" href="#" data-toggle="offcanvas"></a>
                <div class="navbar-custom-menu">
                    <ul class="top-nav">
                        <li class="dropdown">
                            <a class="dropdown-toggle" href="#" data-toggle="dropdown" role="button" aria-haspopup="true" aria-expanded="false">
                                <i class="fa fa-user fa-lg"></i>
                            </a>
                            <ul class="dropdown-menu settings-menu">
                                <li><a href="logout.php"><i class="fa fa-sign-out fa-lg"></i> Logout</a></li>
                            </ul>
                        </li>
                    </ul>
                </div>
            </nav>
        </header>

        <!-- Sidebar -->
        <aside class="main-sidebar hidden-print">
            <section class="sidebar">
                <div class="user-panel">
                    <center>
                        <img class="img-responsive" src="../assets/images/logo.png" alt="Logo" width="250px">
                        <?php
                        $query_unit = mysqli_query($koneksi,"SELECT * FROM unit WHERE ID='$user'");
                        $data_unit = mysqli_fetch_array($query_unit);
                        if($data_unit) {
                            echo "<p style='color:white; margin-top:10px;'>".$data_unit['UNIT']."</p>";
                        } else {
                            echo "<p style='color:white; margin-top:10px;'>ADMINISTRATOR</p>";
                        }
                        ?>
                    </center>
                </div>
                <ul class="sidebar-menu">
                    <li><a href="index.php"><i class="fa fa-home"></i><span>Beranda</span></a></li>
                    <li><a href="dokumen.php"><i class="fa fa-file-text"></i><span>Dokumen</span></a></li>
                    <li><a href="kategori-admin.php"><i class="fa fa-tags"></i><span>Kategori</span></a></li>
                    <li class="active"><a href="master-nomor.php"><i class="fa fa-list-ol"></i><span>Master Nomor</span></a></li>
                    <li><a href="unit-kerja.php"><i class="fa fa-building"></i><span>Unit Kerja</span></a></li>
                    <li><a href="laporan.php"><i class="fa fa-bar-chart"></i><span>Laporan</span></a></li>
                </ul>
            </section>
        </aside>

        <!-- Content Wrapper -->
        <div class="content-wrapper">
            <div class="page-title">
            <div>
              <h1><i class="fa fa-dashboard"></i> Master Nomor Dokumen - Sistem E-Document RSK Dharmais</h1>
            </div>
            <div>
              <ul class="breadcrumb">
                <li><i class="fa fa-home fa-lg"></i></li>
                <li><a href="master-nomor.php">Nomor Dokumen</a></li>
              </ul>
            </div>
          </div>
          

            <section class="content">
                <div class="row">
                    <div class="col-md-12">
                        <div class="card">
                            <div class="card-body">
                                <!-- Form Section -->
                                <div class="form-section">
                                    <h4><i class="fa fa-plus-circle"></i> Tambah Nomor Dokumen</h4>
                                    <hr>
                                    
                                    <!-- Level 1: Parent -->
                                    <div class="level-row">
                                        <div class="row">
                                            <div class="col-md-2">
                                                <label><strong>Parent</strong></label>
                                            </div>
                                            <div class="col-md-8">
                                                <select class="form-control" id="select_parent" name="select_parent">
                                                    <option value="">Pilih Parent</option>
                                                </select>
                                            </div>
                                            <div class="col-md-2">
                                                <button type="button" class="btn btn-add btn-sm" id="btn_add_parent">
                                                    <i class="fa fa-plus"></i>
                                                </button>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Level 2: Sub Parent -->
                                    <div class="level-row">
                                        <div class="row">
                                            <div class="col-md-2">
                                                <label><strong>Sub Parent Lv1</strong></label>
                                            </div>
                                            <div class="col-md-8">
                                                <select class="form-control" id="select_sub_parent" name="select_sub_parent" disabled>
                                                    <option value="">Pilih Sub Parent Lv1</option>
                                                </select>
                                            </div>
                                            <div class="col-md-2">
                                                <button type="button" class="btn btn-add btn-sm" id="btn_add_sub_parent" disabled>
                                                    <i class="fa fa-plus"></i>
                                                </button>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Level 3: Sub Sub Parent -->
                                    <div class="level-row">
                                        <div class="row">
                                            <div class="col-md-2">
                                                <label><strong>Sub Parent Lv2</strong></label>
                                            </div>
                                            <div class="col-md-8">
                                                <select class="form-control" id="select_sub_sub_parent" name="select_sub_sub_parent" disabled>
                                                    <option value="">Pilih Sub Parent Lv2</option>
                                                </select>
                                            </div>
                                            <div class="col-md-2">
                                                <button type="button" class="btn btn-add btn-sm" id="btn_add_sub_sub_parent" disabled>
                                                    <i class="fa fa-plus"></i>
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Tabel Tampilan -->
                                <div class="table-responsive">
                                    <table id="tabel_nomor" class="table table-striped table-bordered" style="width:100%">
                                        <thead>
                                            <tr class="table-head">
                                                <th width="15%">KODE</th>
                                                <th width="35%">NAMA</th>
                                                <th width="25%">PARENT</th>
                                                <th width="10%">URUTAN</th>
                                                <th width="15%">AKSI</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <!-- Data akan dimuat via AJAX -->
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </section>
        </div>
    </div>

    <!-- Modal Tambah -->
    <div class="modal fade" id="modal_tambah" tabindex="-1" role="dialog">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal">&times;</button>
                    <h4 class="modal-title">Tambah Nomor Dokumen</h4>
                </div>
                <form id="form_tambah">
                    <div class="modal-body">
                        <input type="hidden" id="parent_id_input" name="parent_id">
                        <input type="hidden" id="level_input" name="level">
                        
                        <div class="form-group">
                            <label>Nama Dokumen/Kategori</label>
                            <input type="text" class="form-control" id="nama_input" name="nama" required>
                        </div>
                        
                        <div class="form-group">
                            <label>Preview Kode</label>
                            <div class="preview-kode" id="preview_kode">-</div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-default" data-dismiss="modal">Batal</button>
                        <button type="submit" class="btn btn-add">Simpan</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Modal Edit -->
    <div class="modal fade" id="modal_edit" tabindex="-1" role="dialog">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal">&times;</button>
                    <h4 class="modal-title">Edit Nomor Dokumen</h4>
                </div>
                <form id="form_edit">
                    <div class="modal-body">
                        <input type="hidden" id="edit_id" name="id">
                        
                        <div class="form-group">
                            <label>Kode</label>
                            <input type="text" class="form-control" id="edit_kode" name="kode" readonly>
                        </div>
                        
                        <div class="form-group">
                            <label>Nama Dokumen/Kategori</label>
                            <input type="text" class="form-control" id="edit_nama" name="nama" required>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-default" data-dismiss="modal">Batal</button>
                        <button type="submit" class="btn btn-add">Update</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="../assets/js/jquery-2.1.4.min.js"></script>
    <script src="../assets/bootstrap/js/bootstrap.min.js"></script>
    <script src="../assets/plugins/datatables/js/jquery.dataTables.min.js"></script>
    <script src="../assets/plugins/datatables/js/dataTables.bootstrap.min.js"></script>
    <script src="../assets/js/sweetalert.min.js"></script>
    <script src="../assets/js/main.js"></script>

    <script>
    $(document).ready(function() {
        // Inisialisasi DataTable
        var table = $('#tabel_nomor').DataTable({
            "processing": true,
            "serverSide": true,
            "ajax": {
                "url": "ajax/master_nomor_serverside.php",
                "type": "POST"
            },
            "columns": [
                {"data": "KODE", "className": "text-center"},
                {"data": "NAMA", "className": "text-left"},
                {"data": "PARENT_NAMA", "className": "text-center"},
                {"data": "URUTAN", "className": "text-center"},
                {"data": "AKSI", "className": "text-center", "orderable": false, "searchable": false}
            ],
            "order": [[0, "asc"]],
            "language": {
                "url": "../assets/plugins/datatables/Indonesian.json"
            }
        });

        // Load data parent saat halaman dimuat
        loadParentData();

        // Event handler untuk select parent
        $('#select_parent').change(function() {
            var parentId = $(this).val();

            // Reset sub parent dan sub sub parent
            $('#select_sub_parent').html('<option value="">Pilih Sub Parent Lv1</option>').prop('disabled', true);
            $('#select_sub_sub_parent').html('<option value="">Pilih Sub Parent Lv2</option>').prop('disabled', true);

            // Reset tombol
            $('#btn_add_sub_parent').prop('disabled', true);
            $('#btn_add_sub_sub_parent').prop('disabled', true);

            if(parentId) {
                // Enable tombol add sub parent
                $('#btn_add_sub_parent').prop('disabled', false);

                // Load sub parent data
                loadSubParentData(parentId);

                // Enable select sub parent
                $('#select_sub_parent').prop('disabled', false);
            }

            // Update status tombol add parent
            updateAddParentButton();
        });

        // Event handler untuk select sub parent
        $('#select_sub_parent').change(function() {
            var subParentId = $(this).val();

            // Reset sub sub parent
            $('#select_sub_sub_parent').html('<option value="">Pilih Sub Parent Lv2</option>').prop('disabled', true);
            $('#btn_add_sub_sub_parent').prop('disabled', true);

            if(subParentId) {
                // Enable tombol add sub sub parent
                $('#btn_add_sub_sub_parent').prop('disabled', false);

                // Load sub sub parent data
                loadSubSubParentData(subParentId);

                // Enable select sub sub parent
                $('#select_sub_sub_parent').prop('disabled', false);
            }

            // Update status tombol add sub parent
            updateAddSubParentButton();
        });

        // Event handler untuk select sub sub parent
        $('#select_sub_sub_parent').change(function() {
            updateAddSubSubParentButton();
        });

        // Event handler tombol add parent
        $('#btn_add_parent').click(function() {
            showModalTambah(null, 1);
        });

        // Event handler tombol add sub parent
        $('#btn_add_sub_parent').click(function() {
            var parentId = $('#select_parent').val();
            if(parentId) {
                showModalTambah(parentId, 2);
            }
        });

        // Event handler tombol add sub sub parent
        $('#btn_add_sub_sub_parent').click(function() {
            var subParentId = $('#select_sub_parent').val();
            if(subParentId) {
                showModalTambah(subParentId, 3);
            }
        });

        // Form submit tambah
        $('#form_tambah').submit(function(e) {
            e.preventDefault();

            $.ajax({
                url: 'ajax/master_nomor_create.php',
                type: 'POST',
                data: $(this).serialize(),
                dataType: 'json',
                success: function(response) {
                    if(response.success) {
                        $('#modal_tambah').modal('hide');
                        swal({
                            title: 'Berhasil!',
                            text: response.message,
                            type: 'success',
                            timer: 1500,
                            showConfirmButton: false
                        });

                        // Reload data
                        table.ajax.reload();
                        loadParentData();

                        // Reset form
                        $('#form_tambah')[0].reset();
                    } else {
                        swal({
                            title: 'Error!',
                            text: response.message,
                            type: 'error'
                        });
                    }
                },
                error: function() {
                    swal({
                        title: 'Error!',
                        text: 'Terjadi kesalahan sistem',
                        type: 'error'
                    });
                }
            });
        });

        // Form submit edit
        $('#form_edit').submit(function(e) {
            e.preventDefault();

            $.ajax({
                url: 'ajax/master_nomor_update.php',
                type: 'POST',
                data: $(this).serialize(),
                dataType: 'json',
                success: function(response) {
                    if(response.success) {
                        $('#modal_edit').modal('hide');
                        swal({
                            title: 'Berhasil!',
                            text: response.message,
                            type: 'success',
                            timer: 1500,
                            showConfirmButton: false
                        });

                        // Reload data
                        table.ajax.reload();
                        loadParentData();
                    } else {
                        swal({
                            title: 'Error!',
                            text: response.message,
                            type: 'error'
                        });
                    }
                },
                error: function() {
                    swal({
                        title: 'Error!',
                        text: 'Terjadi kesalahan sistem',
                        type: 'error'
                    });
                }
            });
        });

        // Fungsi untuk load data parent
        function loadParentData() {
            $.ajax({
                url: 'ajax/master_nomor_get_parent.php',
                type: 'GET',
                dataType: 'json',
                success: function(data) {
                    var options = '<option value="">Pilih Parent</option>';
                    $.each(data, function(index, item) {
                        options += '<option value="' + item.id + '">' + item.KODE + ' - ' + item.NAMA + '</option>';
                    });
                    $('#select_parent').html(options);
                }
            });
        }

        // Fungsi untuk load data sub parent
        function loadSubParentData(parentId) {
            $.ajax({
                url: 'ajax/master_nomor_get_children.php',
                type: 'POST',
                data: {parent_id: parentId},
                dataType: 'json',
                success: function(data) {
                    var options = '<option value="">Pilih Sub Parent Lv1</option>';
                    $.each(data, function(index, item) {
                        options += '<option value="' + item.id + '">' + item.KODE + ' - ' + item.NAMA + '</option>';
                    });
                    $('#select_sub_parent').html(options);
                }
            });
        }

        // Fungsi untuk load data sub sub parent
        function loadSubSubParentData(subParentId) {
            $.ajax({
                url: 'ajax/master_nomor_get_children.php',
                type: 'POST',
                data: {parent_id: subParentId},
                dataType: 'json',
                success: function(data) {
                    var options = '<option value="">Pilih Sub Parent Lv2</option>';
                    $.each(data, function(index, item) {
                        options += '<option value="' + item.id + '">' + item.KODE + ' - ' + item.NAMA + '</option>';
                    });
                    $('#select_sub_sub_parent').html(options);
                }
            });
        }

        // Fungsi untuk update status tombol add parent
        function updateAddParentButton() {
            var parentSelected = $('#select_parent').val();
            if(parentSelected) {
                $('#btn_add_parent').prop('disabled', true);
            } else {
                $('#btn_add_parent').prop('disabled', false);
            }
        }

        // Fungsi untuk update status tombol add sub parent
        function updateAddSubParentButton() {
            var subParentSelected = $('#select_sub_parent').val();
            if(subParentSelected) {
                $('#btn_add_sub_parent').prop('disabled', true);
            } else {
                var parentSelected = $('#select_parent').val();
                $('#btn_add_sub_parent').prop('disabled', !parentSelected);
            }
        }

        // Fungsi untuk update status tombol add sub sub parent
        function updateAddSubSubParentButton() {
            var subSubParentSelected = $('#select_sub_sub_parent').val();
            if(subSubParentSelected) {
                $('#btn_add_sub_sub_parent').prop('disabled', true);
            } else {
                var subParentSelected = $('#select_sub_parent').val();
                $('#btn_add_sub_sub_parent').prop('disabled', !subParentSelected);
            }
        }

        // Fungsi untuk show modal tambah
        function showModalTambah(parentId, level) {
            $('#parent_id_input').val(parentId);
            $('#level_input').val(level);
            $('#nama_input').val('');

            // Generate preview kode
            generatePreviewKode(parentId, level);

            $('#modal_tambah').modal('show');
        }

        // Fungsi untuk generate preview kode
        function generatePreviewKode(parentId, level) {
            $.ajax({
                url: 'ajax/master_nomor_preview_kode.php',
                type: 'POST',
                data: {parent_id: parentId, level: level},
                dataType: 'json',
                success: function(response) {
                    if(response.success) {
                        $('#preview_kode').text(response.kode);
                    } else {
                        $('#preview_kode').text('Error generating code');
                    }
                }
            });
        }
    });

    // Fungsi untuk edit data (dipanggil dari tombol edit di tabel)
    function editData(id) {
        $.ajax({
            url: 'ajax/master_nomor_get_detail.php',
            type: 'POST',
            data: {id: id},
            dataType: 'json',
            success: function(data) {
                if(data.success) {
                    $('#edit_id').val(data.data.id);
                    $('#edit_kode').val(data.data.KODE);
                    $('#edit_nama').val(data.data.NAMA);
                    $('#modal_edit').modal('show');
                } else {
                    swal({
                        title: 'Error!',
                        text: data.message,
                        type: 'error'
                    });
                }
            }
        });
    }

    // Fungsi untuk hapus data (dipanggil dari tombol hapus di tabel)
    function deleteData(id, kode, nama) {
        swal({
            title: 'Konfirmasi Hapus',
            text: 'Apakah Anda yakin ingin menghapus "' + kode + ' - ' + nama + '"?\nData anak yang terkait juga akan terhapus!',
            type: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#FF6347',
            confirmButtonText: 'Ya, Hapus!',
            cancelButtonText: 'Batal'
        }, function(isConfirm) {
            if(isConfirm) {
                $.ajax({
                    url: 'ajax/master_nomor_delete.php',
                    type: 'POST',
                    data: {id: id},
                    dataType: 'json',
                    success: function(response) {
                        if(response.success) {
                            swal({
                                title: 'Berhasil!',
                                text: response.message,
                                type: 'success',
                                timer: 1500,
                                showConfirmButton: false
                            });

                            // Reload data
                            $('#tabel_nomor').DataTable().ajax.reload();
                            loadParentData();
                        } else {
                            swal({
                                title: 'Error!',
                                text: response.message,
                                type: 'error'
                            });
                        }
                    }
                });
            }
        });
    }
    </script>
</body>
</html>
