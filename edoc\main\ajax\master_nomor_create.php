<?php
session_start();
include '../../config.php';
error_reporting(E_ALL ^ (E_NOTICE | E_WARNING));

// Cek session
if(empty($_SESSION['username'])){
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'Unauthorized']);
    exit;
}

$user = $_SESSION['username'];
$hak_akses = $_SESSION['hak_akses'];

// Hanya admin yang bisa akses
if($hak_akses != '1'){ 
    http_response_code(403);
    echo json_encode(['success' => false, 'message' => 'Forbidden']);
    exit;
}

// Validasi input
if (!isset($_POST['nama']) || empty(trim($_POST['nama']))) {
    echo json_encode(['success' => false, 'message' => 'Nama dokumen/kategori harus diisi']);
    exit;
}

if (!isset($_POST['level']) || !in_array($_POST['level'], [1, 2, 3])) {
    echo json_encode(['success' => false, 'message' => 'Level tidak valid']);
    exit;
}

$nama = mysqli_real_escape_string($koneksi, trim($_POST['nama']));
$parent_id = isset($_POST['parent_id']) && !empty($_POST['parent_id']) ? intval($_POST['parent_id']) : null;
$level = intval($_POST['level']);

// Validasi parent_id untuk level 2 dan 3
if ($level > 1 && ($parent_id === null || $parent_id <= 0)) {
    echo json_encode(['success' => false, 'message' => 'Parent ID harus diisi untuk level ' . $level]);
    exit;
}

// Validasi parent_id untuk level 1
if ($level == 1 && $parent_id !== null) {
    echo json_encode(['success' => false, 'message' => 'Level 1 tidak boleh memiliki parent']);
    exit;
}

try {
    // Mulai transaksi
    mysqli_autocommit($koneksi, false);
    
    // Generate kode dan urutan menggunakan stored procedure
    $kode_baru = '';
    $urutan_baru = 1;
    
    try {
        $stmt = mysqli_prepare($koneksi, "CALL sp_generate_kode_nomor(?, ?, @kode_baru, @urutan_baru)");
        mysqli_stmt_bind_param($stmt, "ii", $parent_id, $level);
        
        if (mysqli_stmt_execute($stmt)) {
            mysqli_stmt_close($stmt);
            
            // Ambil hasil dari output parameter
            $result = mysqli_query($koneksi, "SELECT @kode_baru as kode_baru, @urutan_baru as urutan_baru");
            $row = mysqli_fetch_assoc($result);
            $kode_baru = $row['kode_baru'];
            $urutan_baru = $row['urutan_baru'];
        } else {
            throw new Exception('Gagal menjalankan stored procedure');
        }
    } catch (Exception $e) {
        // Fallback manual jika stored procedure gagal
        if ($level == 1) {
            // Level 1: Generate kode 2 huruf
            $query = "SELECT COALESCE(MAX(URUTAN), 0) as max_urutan FROM edoc_nomor WHERE PARENT_ID IS NULL AND STATUS = 1";
            $result = mysqli_query($koneksi, $query);
            $row = mysqli_fetch_assoc($result);
            $urutan_baru = $row['max_urutan'] + 1;
            
            // Generate kode berdasarkan urutan
            $kode_map = [
                1 => 'AR', 2 => 'BD', 3 => 'KU', 4 => 'AD', 5 => 'OP',
                6 => 'TK', 7 => 'HU', 8 => 'KE', 9 => 'PE', 10 => 'PR'
            ];
            
            $kode_baru = isset($kode_map[$urutan_baru]) ? $kode_map[$urutan_baru] : 'K' . str_pad($urutan_baru, 2, '0', STR_PAD_LEFT);
            
        } else {
            // Level 2 dan 3: Ambil kode parent dan tambahkan nomor urut
            $query = "SELECT KODE FROM edoc_nomor WHERE id = $parent_id AND STATUS = 1";
            $result = mysqli_query($koneksi, $query);
            $parent_row = mysqli_fetch_assoc($result);
            
            if (!$parent_row) {
                throw new Exception('Parent tidak ditemukan');
            }
            
            $parent_kode = $parent_row['KODE'];
            
            // Cari urutan terakhir untuk parent ini
            $query = "SELECT COALESCE(MAX(URUTAN), 0) as max_urutan FROM edoc_nomor WHERE PARENT_ID = $parent_id AND STATUS = 1";
            $result = mysqli_query($koneksi, $query);
            $row = mysqli_fetch_assoc($result);
            $urutan_baru = $row['max_urutan'] + 1;
            
            $kode_baru = $parent_kode . '.' . str_pad($urutan_baru, 2, '0', STR_PAD_LEFT);
        }
    }
    
    // Cek apakah kode sudah ada
    $check_query = "SELECT id FROM edoc_nomor WHERE KODE = '$kode_baru' AND STATUS = 1";
    $check_result = mysqli_query($koneksi, $check_query);
    
    if (mysqli_num_rows($check_result) > 0) {
        throw new Exception('Kode ' . $kode_baru . ' sudah ada');
    }
    
    // Insert data baru
    $parent_id_sql = $parent_id ? $parent_id : 'NULL';
    $insert_query = "INSERT INTO edoc_nomor (KODE, NAMA, PARENT_ID, URUTAN, LEVEL, STATUS, CREATED_BY, CREATED_AT) 
                     VALUES ('$kode_baru', '$nama', $parent_id_sql, $urutan_baru, $level, 1, '$user', NOW())";
    
    if (!mysqli_query($koneksi, $insert_query)) {
        throw new Exception('Gagal menyimpan data: ' . mysqli_error($koneksi));
    }
    
    // Commit transaksi
    mysqli_commit($koneksi);
    mysqli_autocommit($koneksi, true);
    
    echo json_encode([
        'success' => true, 
        'message' => 'Data berhasil disimpan dengan kode: ' . $kode_baru,
        'kode' => $kode_baru
    ]);
    
} catch (Exception $e) {
    // Rollback transaksi
    mysqli_rollback($koneksi);
    mysqli_autocommit($koneksi, true);
    
    echo json_encode([
        'success' => false, 
        'message' => $e->getMessage()
    ]);
}
?>
