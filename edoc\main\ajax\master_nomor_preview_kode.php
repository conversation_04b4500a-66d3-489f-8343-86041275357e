<?php
session_start();
include '../../config.php';
error_reporting(E_ALL ^ (E_NOTICE | E_WARNING));

// Cek session
if(empty($_SESSION['username'])){
    http_response_code(401);
    echo json_encode(['error' => 'Unauthorized']);
    exit;
}

$user = $_SESSION['username'];
$hak_akses = $_SESSION['hak_akses'];

// <PERSON>ya admin yang bisa akses
if($hak_akses != '1'){ 
    http_response_code(403);
    echo json_encode(['error' => 'Forbidden']);
    exit;
}

// Ambil parameter
$parent_id = isset($_POST['parent_id']) ? intval($_POST['parent_id']) : null;
$level = isset($_POST['level']) ? intval($_POST['level']) : 1;

try {
    // Panggil stored procedure untuk generate kode
    $stmt = mysqli_prepare($koneksi, "CALL sp_generate_kode_nomor(?, ?, @kode_baru, @urutan_baru)");
    mysqli_stmt_bind_param($stmt, "ii", $parent_id, $level);
    
    if (mysqli_stmt_execute($stmt)) {
        mysqli_stmt_close($stmt);
        
        // Ambil hasil dari output parameter
        $result = mysqli_query($koneksi, "SELECT @kode_baru as kode_baru, @urutan_baru as urutan_baru");
        $row = mysqli_fetch_assoc($result);
        
        echo json_encode([
            'success' => true,
            'kode' => $row['kode_baru'],
            'urutan' => $row['urutan_baru']
        ]);
    } else {
        throw new Exception('Gagal menjalankan stored procedure');
    }
    
} catch (Exception $e) {
    // Fallback manual jika stored procedure gagal
    $kode_baru = '';
    $urutan_baru = 1;
    
    if ($level == 1) {
        // Level 1: Generate kode 2 huruf
        $query = "SELECT COALESCE(MAX(URUTAN), 0) as max_urutan FROM edoc_nomor WHERE PARENT_ID IS NULL AND STATUS = 1";
        $result = mysqli_query($koneksi, $query);
        $row = mysqli_fetch_assoc($result);
        $urutan_baru = $row['max_urutan'] + 1;
        
        // Generate kode berdasarkan urutan
        $kode_map = [
            1 => 'AR', 2 => 'BD', 3 => 'KU', 4 => 'AD', 5 => 'OP',
            6 => 'TK', 7 => 'HU', 8 => 'KE', 9 => 'PE', 10 => 'PR'
        ];
        
        $kode_baru = isset($kode_map[$urutan_baru]) ? $kode_map[$urutan_baru] : 'K' . str_pad($urutan_baru, 2, '0', STR_PAD_LEFT);
        
    } else {
        // Level 2 dan 3: Ambil kode parent dan tambahkan nomor urut
        if ($parent_id) {
            $query = "SELECT KODE FROM edoc_nomor WHERE id = $parent_id AND STATUS = 1";
            $result = mysqli_query($koneksi, $query);
            $parent_row = mysqli_fetch_assoc($result);
            
            if ($parent_row) {
                $parent_kode = $parent_row['KODE'];
                
                // Cari urutan terakhir untuk parent ini
                $query = "SELECT COALESCE(MAX(URUTAN), 0) as max_urutan FROM edoc_nomor WHERE PARENT_ID = $parent_id AND STATUS = 1";
                $result = mysqli_query($koneksi, $query);
                $row = mysqli_fetch_assoc($result);
                $urutan_baru = $row['max_urutan'] + 1;
                
                $kode_baru = $parent_kode . '.' . str_pad($urutan_baru, 2, '0', STR_PAD_LEFT);
            }
        }
    }
    
    echo json_encode([
        'success' => true,
        'kode' => $kode_baru,
        'urutan' => $urutan_baru
    ]);
}
?>
