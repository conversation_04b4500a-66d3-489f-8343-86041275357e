<?php
session_start();
include '../../config.php';
error_reporting(E_ALL ^ (E_NOTICE | E_WARNING));

// Cek session
if(empty($_SESSION['username'])){
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'Unauthorized']);
    exit;
}

$user = $_SESSION['username'];
$hak_akses = $_SESSION['hak_akses'];

// Hanya admin yang bisa akses
if($hak_akses != '1'){ 
    http_response_code(403);
    echo json_encode(['success' => false, 'message' => 'Forbidden']);
    exit;
}

// Validasi input
if (!isset($_POST['id']) || empty($_POST['id'])) {
    echo json_encode(['success' => false, 'message' => 'ID tidak valid']);
    exit;
}

if (!isset($_POST['nama']) || empty(trim($_POST['nama']))) {
    echo json_encode(['success' => false, 'message' => 'Nama dokumen/kategori harus diisi']);
    exit;
}

$id = intval($_POST['id']);
$nama = mysqli_real_escape_string($koneksi, trim($_POST['nama']));

try {
    // Cek apakah data exists
    $check_query = "SELECT id, KODE FROM edoc_nomor WHERE id = $id AND STATUS = 1";
    $check_result = mysqli_query($koneksi, $check_query);
    
    if (mysqli_num_rows($check_result) == 0) {
        throw new Exception('Data tidak ditemukan');
    }
    
    $existing_data = mysqli_fetch_assoc($check_result);
    
    // Update data (hanya nama yang bisa diubah, kode tetap)
    $update_query = "UPDATE edoc_nomor SET 
                     NAMA = '$nama',
                     UPDATED_AT = NOW()
                     WHERE id = $id AND STATUS = 1";
    
    if (!mysqli_query($koneksi, $update_query)) {
        throw new Exception('Gagal mengupdate data: ' . mysqli_error($koneksi));
    }
    
    if (mysqli_affected_rows($koneksi) == 0) {
        throw new Exception('Tidak ada perubahan data');
    }
    
    echo json_encode([
        'success' => true, 
        'message' => 'Data berhasil diupdate'
    ]);
    
} catch (Exception $e) {
    echo json_encode([
        'success' => false, 
        'message' => $e->getMessage()
    ]);
}
?>
