<?php
session_start();
include '../../config.php';
error_reporting(E_ALL ^ (E_NOTICE | E_WARNING));

// Cek session
if(empty($_SESSION['username'])){
    http_response_code(401);
    echo json_encode(['error' => 'Unauthorized']);
    exit;
}

$user = $_SESSION['username'];
$hak_akses = $_SESSION['hak_akses'];

// <PERSON>ya admin yang bisa akses
if($hak_akses != '1'){ 
    http_response_code(403);
    echo json_encode(['error' => 'Forbidden']);
    exit;
}

// Fungsi untuk escape string SQL
function escapeString($koneksi, $string) {
    return mysqli_real_escape_string($koneksi, $string);
}

// ===== PARAMETER DATATABLES =====
$draw = intval($_POST['draw']);
$start = intval($_POST['start']);
$length = intval($_POST['length']);
$searchValue = escapeString($koneksi, $_POST['search']['value']);

// ===== KOLOM UNTUK ORDERING =====
$orderColumns = array(
    0 => 'n.KODE',
    1 => 'n.NAMA',
    2 => 'p.NAMA',
    3 => 'n.URUTAN'
);

// ===== KONDISI SEARCH =====
$searchClause = "";
if (!empty($searchValue)) {
    $searchClause = " AND (
        n.KODE LIKE '%$searchValue%' OR
        n.NAMA LIKE '%$searchValue%' OR
        p.NAMA LIKE '%$searchValue%' OR
        n.URUTAN LIKE '%$searchValue%'
    )";
}

// ===== KONDISI ORDER =====
$orderCol = isset($orderColumns[$_POST['order'][0]['column']]) ? $orderColumns[$_POST['order'][0]['column']] : 'n.KODE';
$orderDir = in_array(strtoupper($_POST['order'][0]['dir']), ['ASC', 'DESC']) ? $_POST['order'][0]['dir'] : 'ASC';

// ===== QUERY UTAMA =====
$sql = "
SELECT
    SQL_CALC_FOUND_ROWS
    n.id,
    n.KODE,
    n.NAMA,
    n.PARENT_ID,
    CASE 
        WHEN n.PARENT_ID IS NULL THEN '-'
        ELSE p.NAMA 
    END AS PARENT_NAMA,
    n.URUTAN,
    n.LEVEL,
    n.STATUS,
    n.CREATED_BY,
    n.CREATED_AT
FROM edoc_nomor n
LEFT JOIN edoc_nomor p ON n.PARENT_ID = p.id
WHERE n.STATUS = 1 $searchClause
ORDER BY $orderCol $orderDir
LIMIT $start, $length
";

$result = mysqli_query($koneksi, $sql);
if (!$result) {
    die(json_encode(['error' => 'Query Error: ' . mysqli_error($koneksi)]));
}

// Total filtered
$countFiltered = mysqli_query($koneksi, "SELECT FOUND_ROWS() as count");
$recordsFiltered = mysqli_fetch_assoc($countFiltered)['count'];

// Total all (tanpa search)
$countTotal = mysqli_query($koneksi, "
    SELECT COUNT(*) as count 
    FROM edoc_nomor n 
    LEFT JOIN edoc_nomor p ON n.PARENT_ID = p.id
    WHERE n.STATUS = 1
");
$recordsTotal = mysqli_fetch_assoc($countTotal)['count'];

// Format data
$data = [];
while ($row = mysqli_fetch_assoc($result)) {
    $nestedData = [];
    $nestedData['KODE'] = $row['KODE'];
    $nestedData['NAMA'] = $row['NAMA'];
    $nestedData['PARENT_NAMA'] = $row['PARENT_NAMA'];
    $nestedData['URUTAN'] = $row['URUTAN'];
    
    // Tombol aksi
    $aksi = '
        <button type="button" class="btn btn-primary btn-xs" onclick="editData(' . $row['id'] . ')" title="Edit">
            <i class="fa fa-edit"></i>
        </button>
        <button type="button" class="btn btn-danger btn-xs" onclick="deleteData(' . $row['id'] . ', \'' . addslashes($row['KODE']) . '\', \'' . addslashes($row['NAMA']) . '\')" title="Hapus">
            <i class="fa fa-trash"></i>
        </button>
    ';
    
    $nestedData['AKSI'] = $aksi;
    
    $data[] = $nestedData;
}

// Response JSON
$response = array(
    "draw" => $draw,
    "recordsTotal" => intval($recordsTotal),
    "recordsFiltered" => intval($recordsFiltered),
    "data" => $data
);

header('Content-Type: application/json');
echo json_encode($response);
?>
