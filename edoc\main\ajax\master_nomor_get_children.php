<?php
session_start();
include '../../config.php';
error_reporting(E_ALL ^ (E_NOTICE | E_WARNING));

// Cek session
if(empty($_SESSION['username'])){
    http_response_code(401);
    echo json_encode(['error' => 'Unauthorized']);
    exit;
}

$user = $_SESSION['username'];
$hak_akses = $_SESSION['hak_akses'];

// Hanya admin yang bisa akses
if($hak_akses != '1'){ 
    http_response_code(403);
    echo json_encode(['error' => 'Forbidden']);
    exit;
}

// Ambil parent_id dari POST
$parent_id = isset($_POST['parent_id']) ? intval($_POST['parent_id']) : 0;

if ($parent_id <= 0) {
    echo json_encode([]);
    exit;
}

// Query untuk mendapatkan data children berdasarkan parent_id
$query = "SELECT id, KODE, NAMA FROM edoc_nomor WHERE PARENT_ID = $parent_id AND STATUS = 1 ORDER BY KODE ASC";
$result = mysqli_query($koneksi, $query);

$data = [];
if ($result) {
    while ($row = mysqli_fetch_assoc($result)) {
        $data[] = $row;
    }
}

header('Content-Type: application/json');
echo json_encode($data);
?>
