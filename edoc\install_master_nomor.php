<?php
/**
 * <PERSON>ript untuk menginstall tabel edoc_nomor dan data sample
 * Jalankan file ini sekali untuk membuat tabel dan data awal
 */

include 'config.php';

echo "<h2>Instalasi Master Nomor Dokumen</h2>";
echo "<hr>";

try {
    // Baca file SQL
    $sql_file = 'database/edoc_nomor.sql';
    
    if (!file_exists($sql_file)) {
        throw new Exception("File SQL tidak ditemukan: $sql_file");
    }
    
    $sql_content = file_get_contents($sql_file);
    
    // Split SQL berdasarkan delimiter
    $sql_statements = explode(';', $sql_content);
    
    $success_count = 0;
    $error_count = 0;
    
    foreach ($sql_statements as $statement) {
        $statement = trim($statement);
        
        // Skip empty statements dan comments
        if (empty($statement) || strpos($statement, '--') === 0 || strpos($statement, '/*') === 0) {
            continue;
        }
        
        // Skip DELIMITER statements
        if (strpos($statement, 'DELIMITER') === 0) {
            continue;
        }
        
        // Handle stored procedure
        if (strpos($statement, 'CREATE PROCEDURE') !== false) {
            // Untuk stored procedure, kita perlu menggabungkan sampai END
            $proc_sql = $statement;
            continue;
        }
        
        if (strpos($statement, 'END$$') !== false) {
            $proc_sql .= '; ' . $statement;
            $statement = $proc_sql;
        }
        
        echo "<p>Menjalankan: " . substr($statement, 0, 100) . "...</p>";
        
        if (mysqli_query($koneksi, $statement)) {
            echo "<p style='color: green;'>✓ Berhasil</p>";
            $success_count++;
        } else {
            echo "<p style='color: red;'>✗ Error: " . mysqli_error($koneksi) . "</p>";
            $error_count++;
        }
        
        echo "<hr>";
    }
    
    echo "<h3>Hasil Instalasi:</h3>";
    echo "<p>Berhasil: $success_count statement</p>";
    echo "<p>Error: $error_count statement</p>";
    
    if ($error_count == 0) {
        echo "<p style='color: green; font-weight: bold;'>✓ Instalasi berhasil! Tabel edoc_nomor dan data sample telah dibuat.</p>";
        echo "<p><a href='main/master-nomor.php'>Akses Master Nomor Dokumen</a></p>";
    } else {
        echo "<p style='color: red; font-weight: bold;'>⚠ Instalasi selesai dengan beberapa error. Silakan periksa log di atas.</p>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red; font-weight: bold;'>Error: " . $e->getMessage() . "</p>";
}

echo "<br><br>";
echo "<p><a href='main/index.php'>Kembali ke Dashboard</a></p>";
?>

<!DOCTYPE html>
<html>
<head>
    <title>Instalasi Master Nomor Dokumen</title>
    <meta charset="utf-8">
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        h2 { color: #333; }
        p { margin: 5px 0; }
        hr { margin: 10px 0; }
        a { color: #007bff; text-decoration: none; }
        a:hover { text-decoration: underline; }
    </style>
</head>
<body>
    <!-- Content sudah di-output di atas -->
</body>
</html>
