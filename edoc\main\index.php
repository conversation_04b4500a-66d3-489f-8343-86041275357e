<?php
// test percobaan
session_start();
include '../config.php';
error_reporting(E_ALL ^ (E_NOTICE | E_WARNING));
if(empty($_SESSION['username'])){
header("location:../index.php");}
$user = $_SESSION['username'];
$hak_akses = $_SESSION['hak_akses'];
if($hak_akses == '1' || $hak_akses =='2'){ ?>
        <!DOCTYPE html>
        <html>
          <head>
            <title>Edoc - Dashboard</title>
            <meta charset="utf-8">
            <meta http-equiv="X-UA-Compatible" content="IE=edge">
            <meta name="viewport" content="width=device-width, initial-scale=1">
            <link rel="stylesheet" type="text/css" href="../assets/css/main.css">
            <link rel="stylesheet" type="text/css" href="../assets/bootstrap/css/bootstrap.min.css">
            <link rel="stylesheet" type="text/css" href="../assets/plugins/datatables/css/dataTables.bootstrap.min.css">
            <link rel="stylesheet" type="text/css" href="../assets/css/sweetalert.css">
            <link rel="shortcut icon" href="../assets/images/favicon.ico" type="image/x-icon">
            <link rel="icon" href="../assets/images/favicon.ico" type="image/x-icon">
            <!-- HTML5 Shim and Respond.js IE8 support of HTML5 elements and media queries-->
            <!--if lt IE 9
            script(src='https://oss.maxcdn.com/libs/html5shiv/3.7.0/html5shiv.js')
            script(src='https://oss.maxcdn.com/libs/respond.js/1.4.2/respond.min.js')
            -->
          </head>
          <body class="sidebar-mini fixed">
            <div class="wrapper">
              <header class="main-header hidden-print"><a class="logo" href="index.php" style="font-size:13pt">RSK Dharmais</a>
                <nav class="navbar navbar-static-top">
                  <a class="sidebar-toggle" href="#" data-toggle="offcanvas"></a>
                  <div class="navbar-custom-menu">
                    <ul class="top-nav">
                      <li class="dropdown"><a class="dropdown-toggle" href="#" data-toggle="dropdown" role="button" aria-haspopup="true" aria-expanded="false"><i class="fa fa-user fa-lg"></i></a>
                        <ul class="dropdown-menu settings-menu">
                          <li><a href="logout.php"><i class="fa fa-sign-out fa-lg"></i> Logout</a></li>
                        </ul>
                      </li>
                    </ul>
                  </div>
                </nav>
              </header>
              <aside class="main-sidebar hidden-print">
              <section class="sidebar">
                <div class="user-panel">
                  <center>
                    <img class="img-responsive" src="../assets/images/logo.png" alt="Logo" width="250px">
                    <?php
                    $qpersonal = mysqli_query($koneksi,"SELECT FULL_NAME FROM edoc_admin WHERE USERNAME='$user'");
                    $datapersonal = mysqli_fetch_array($qpersonal);
                    $fullname = $datapersonal['FULL_NAME'];
                    $qunit = mysqli_query($koneksi,"SELECT UNIT FROM unit WHERE ID='$user'");
                    $dataunit = mysqli_fetch_array($qunit);
                    ?>
                    <?php if ($hak_akses == '1') { ?>
                      <p style="font-size:14pt;color:white;"><b><?php echo $fullname; ?></b><br>
                      <span>Full Administator E-Doc</span> </p>
                    <?php }elseif ($hak_akses == '2') { ?>
                      <p style="font-size:12pt;color:white;"><b>Administrator E-Doc</b><br>
                      <span style="font-size:6pt"><?php echo $dataunit['UNIT'];?></span> </p>
                    <?php } ?>
                  </center>
                  <div class="pull-left info" style="color:white">
                    <br>
                    <br>
                    <br>
                  </div>
                </div>
                <ul class="sidebar-menu">
                <li class="active"><a href='index.php'><i class='fa fa-dashboard'></i><span>Dashboard</span></a></li>
                <?php if ($hak_akses == '1'){ ?>
                <li><a href='kategori-admin.php'><i class='fa fa-list-alt'></i><span>Data Kategori Unit Kerja</span></a></li>
                <li><a href='master-nomor.php'><i class='fa fa-list-ol'></i><span>Master Nomor Dokumen</span></a></li>
                <li><a href='unit-kerja.php'><i class='fa fa-list-alt'></i><span>Data Unit Kerja</span></a></li>
                <li><a href='kumpulan_sk/index.php'><i class='fa fa-list-alt'></i><span>Data Kumpulan SK</span></a></li>
                <!-- <li>
                    <a href='dokumen_nonaktif.php'>
                        <i class='fa fa-list-alt'></i>
                        <span>Dokumen Kadaluwarsa</span><br>
                        <span style="margin-left: 28px;">(Arsip Inaktif)</span>
                    </a>
                </li> -->
                <li><a href='dokumen_nonaktif.php'><i class='fa fa-list-alt'></i><span>Dokumen Kadaluwarsa</span><br>
                <span style="margin-left: 26px;">(Arsip Inaktif)</span></a></li>

                <?php } ?>
                <?php if ($hak_akses == '2'){ ?>
                  <?php //if ($user == 5002 || $user == 'admin') { ?>
                    <li><a href='kategori.php'><i class='fa fa-list-alt'></i><span>Data Kategori</span></a></li>
                  <?php //} ?>
                <li><a href='dokumen.php'><i class='fa fa-list-alt'></i><span>Data Dokumen</span></a></li>
                <li><a href='edoc_dokumentasi/index.php'><i class='fa fa-camera'></i><span>Dokumentasi</span></a></li>
                <?php } ?>
                <?php if ($hak_akses == '1'){ ?>
                <li><a href='edoc_dokumentasi/index.php'><i class='fa fa-camera'></i><span>Dokumentasi</span></a></li>
                <li><a href='berkas-file.php'><i class='fa fa-folder-open'></i><span>Berkas File</span></a></li>
                <li><a href='laporan.php'><i class='fa fa-list-alt'></i><span>Laporan</span></a></li>
                <?php } ?>
                </ul>
              </section>
              </aside>
              <div class="content-wrapper">
                <div class="page-title">
                  <div>
                    <h1><i class="fa fa-dashboard"></i> Dashboard - Sistem E-Document RSK Dharmais</h1>
                  </div>
                  <div>
                    <ul class="breadcrumb">
                      <li><i class="fa fa-home fa-lg"></i></li>
                      <li><a href="index.php">Dashboard</a></li>
                    </ul>
                  </div>
                </div>
                <div class="row">
                  <div class="col-md-12">
                    <div class="card">
                      <div class="card-body">
                          <center>
                            <img class="img-responsive" src="../assets/images/logo.png" alt="Logo" width="300px">
                            <h1>Selamat Datang</h1></center>
                      </div>
                    </div>
                  </div>
                  <div class="col-md-12">
                    <div class="card">
                      <div class="card-body">
                        <footer>
                        <div class="container-fluid">
                          <p class="copyright">Copyright &copy; 2018 - <?php echo date ('Y'); ?> ALL RIGHT RESERVED. Sistem E-Document RSK Dharmais.<br><span style="font-size:8pt;color:grey">Last Update: 20 Mei 2019</span></p>
                        </div>
                      </footer>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <script src="../assets/js/jquery-2.1.4.min.js"></script>
            <script src="../assets/js/jquery-ui.js"></script>
            <script src="../assets/js/essential-plugins.js"></script>
            <script src="../assets/bootstrap/js/bootstrap.min.js"></script>
            <script src="../assets/plugins/datatables/js/jquery.dataTables.js"></script>
            <script src="../assets/plugins/datatables/js/dataTables.bootstrap.min.js" charset="utf-8"></script>
            <script src="../assets/js/pace.min.js"></script>
            <script src="../assets/js/main.js"></script>
            <script src="../assets/js/sweetalert.min.js"></script>
          </body>
        </html>
<?php }else { ?>
  <!DOCTYPE html>
  <html lang="en" dir="ltr">
    <head>
      <meta charset="utf-8">
      <title>Notice!</title>
      <link rel="stylesheet" type="text/css" href="../assets/css/sweetalert.css">
      <script src="../assets/js/sweetalert.min.js"></script>
    </head>
    <body>
    </body>
  </html>
  <script>
        setTimeout(function() {
        swal({
            title: 'Notice',
            text: 'Anda tidak memiliki Akses!',
            type: 'error',
            timer: 2000,
            showConfirmButton: false
        }, function() {
            window.location = 'login.php';
        });
        });
        </script>
<?php }?>
