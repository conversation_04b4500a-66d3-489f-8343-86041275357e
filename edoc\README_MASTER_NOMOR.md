# Master Nomor Dokumen - Sistem Hierarki 3 Level

## 📋 Deskripsi
Sistem manajemen nomor dokumen dengan struktur hierarki 3 level (Parent → Sub Parent → Sub Sub Parent) yang memungkinkan penambahan dinamis berdasarkan level dan menampilkan kode secara otomatis sesuai urutan.

## 🏗️ Struktur Tabel `edoc_nomor`

| Kolom | Tipe | Deskripsi |
|-------|------|-----------|
| id | INT | Auto Increment (Primary Key) |
| KODE | VARCHAR(50) | Kode dokumen hierarkis (AR, AR.01, AR.01.01) |
| NAMA | VARCHAR(255) | Nama dokumen/kategori |
| PARENT_ID | INT | ID parent (NULL untuk level 1) |
| URUTAN | INT | Urutan relatif terhadap parent |
| LEVEL | TINYINT | Level hierarki (1=Parent, 2=Sub Parent, 3=Sub Sub Parent) |
| STATUS | TINYINT | Status aktif (1=aktif, 0=nonaktif) |
| CREATED_BY | VARCHAR(50) | User yang membuat |
| CREATED_AT | TIMESTAMP | Waktu dibuat |
| UPDATED_AT | TIMESTAMP | Waktu diupdate |

## 🎯 Fitur Utama

### 🖼️ UI Form Input (3 Baris Vertikal)
1. **PARENT** - SelectOption parent + Button Tambah Parent
2. **SUB PARENT (LV1)** - SelectOption sub parent + Button Tambah Sub Parent  
3. **SUB SUB PARENT (LV2)** - SelectOption sub-sub parent + Button Tambah Sub Sub Parent

### 🔒 Logika Tombol
- Tombol plus (+) hanya aktif saat level atasnya sudah dipilih
- Tombol dinonaktifkan jika sudah memilih anak di bawahnya
- Warna tombol: #FF6347 (tomato)

### 🔁 Pembentukan Kode Otomatis
- **Level 1**: Kode 2 huruf (AR, BD, KU, dll)
- **Level 2**: Parent.XX (AR.01, AR.02, dll)
- **Level 3**: Parent.Sub.XX (AR.01.01, AR.01.02, dll)

### 📋 Tabel Tampilan
Menampilkan data dengan kolom:
- KODE
- NAMA  
- PARENT
- URUTAN
- AKSI (Edit/Delete)

## 🚀 Instalasi

### 1. Jalankan Script Database
```bash
# Akses melalui browser
http://localhost/edoc/install_master_nomor.php
```

### 2. File yang Dibuat
```
edoc/
├── database/
│   └── edoc_nomor.sql              # Script database
├── main/
│   ├── master-nomor.php            # Halaman utama
│   └── ajax/
│       ├── master_nomor_serverside.php     # DataTables server-side
│       ├── master_nomor_get_parent.php     # Get data parent
│       ├── master_nomor_get_children.php   # Get data children
│       ├── master_nomor_preview_kode.php   # Preview kode baru
│       ├── master_nomor_create.php         # Create data
│       ├── master_nomor_get_detail.php     # Get detail data
│       ├── master_nomor_update.php         # Update data
│       └── master_nomor_delete.php         # Delete data
├── install_master_nomor.php        # Script instalasi
└── README_MASTER_NOMOR.md          # Dokumentasi ini
```

### 3. Akses Menu
- Login sebagai admin
- Menu: **Master Nomor Dokumen** (sidebar)

## 🔧 Cara Penggunaan

### Menambah Data Level 1 (Parent)
1. Pastikan dropdown Parent kosong
2. Klik tombol **+** di sebelah dropdown Parent
3. Isi nama dokumen/kategori
4. Sistem akan generate kode otomatis (AR, BD, KU, dll)
5. Klik **Simpan**

### Menambah Data Level 2 (Sub Parent)
1. Pilih Parent dari dropdown
2. Klik tombol **+** di sebelah dropdown Sub Parent Lv1
3. Isi nama dokumen/kategori
4. Sistem akan generate kode otomatis (AR.01, AR.02, dll)
5. Klik **Simpan**

### Menambah Data Level 3 (Sub Sub Parent)
1. Pilih Parent dari dropdown
2. Pilih Sub Parent dari dropdown
3. Klik tombol **+** di sebelah dropdown Sub Parent Lv2
4. Isi nama dokumen/kategori
5. Sistem akan generate kode otomatis (AR.01.01, AR.01.02, dll)
6. Klik **Simpan**

## 📊 Contoh Data Hierarki

```
AR (Kearsipan)
├── AR.01 (Penciptaan Arsip)
│   ├── AR.01.01 (Pengelolaan Surat Masuk)
│   └── AR.01.02 (Pengelolaan Surat Keluar)
└── AR.02 (Pengelolaan Arsip)
    └── AR.02.01 (Penyimpanan Arsip)

BD (Bidang)
├── BD.01 (Bidang Medik)
│   ├── BD.01.01 (Pelayanan Medis)
│   └── BD.01.02 (Tindakan Medis)
└── BD.02 (Bidang Keperawatan)

KU (Keuangan)
├── KU.01 (Anggaran)
│   ├── KU.01.01 (Perencanaan Anggaran)
│   └── KU.01.02 (Pelaksanaan Anggaran)
└── KU.02 (Pembayaran)
```

## 🛠️ Teknologi yang Digunakan

- **Backend**: PHP 7.4+, MySQL 5.7+
- **Frontend**: HTML5, CSS3, JavaScript (jQuery)
- **UI Framework**: Bootstrap 3.x
- **DataTables**: Server-side processing
- **Notifications**: SweetAlert2 v11
- **Icons**: Font Awesome

## 🔐 Keamanan

- Hanya admin (hak_akses = '1') yang dapat mengakses
- Validasi input pada semua form
- Prepared statements untuk mencegah SQL injection
- Session management untuk autentikasi

## 🐛 Troubleshooting

### Error "Stored procedure tidak ditemukan"
- Jalankan ulang script instalasi
- Pastikan user database memiliki privilege CREATE ROUTINE

### Tombol tidak aktif
- Periksa JavaScript console untuk error
- Pastikan jQuery dan dependencies ter-load dengan benar

### Data tidak muncul di tabel
- Periksa file ajax/master_nomor_serverside.php
- Cek koneksi database di config.php

## 📞 Support

Untuk pertanyaan atau masalah, silakan hubungi tim development atau buat issue di repository project.

---
**Dibuat untuk RSK Dharmais - Sistem E-Document**
