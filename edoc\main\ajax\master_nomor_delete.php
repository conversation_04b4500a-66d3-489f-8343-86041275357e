<?php
session_start();
include '../../config.php';
error_reporting(E_ALL ^ (E_NOTICE | E_WARNING));

// Cek session
if(empty($_SESSION['username'])){
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'Unauthorized']);
    exit;
}

$user = $_SESSION['username'];
$hak_akses = $_SESSION['hak_akses'];

// Hanya admin yang bisa akses
if($hak_akses != '1'){ 
    http_response_code(403);
    echo json_encode(['success' => false, 'message' => 'Forbidden']);
    exit;
}

// Validasi input
if (!isset($_POST['id']) || empty($_POST['id'])) {
    echo json_encode(['success' => false, 'message' => 'ID tidak valid']);
    exit;
}

$id = intval($_POST['id']);

try {
    // <PERSON><PERSON> transaksi
    mysqli_autocommit($koneksi, false);
    
    // Cek apakah data exists
    $check_query = "SELECT id, KODE, NAMA FROM edoc_nomor WHERE id = $id AND STATUS = 1";
    $check_result = mysqli_query($koneksi, $check_query);
    
    if (mysqli_num_rows($check_result) == 0) {
        throw new Exception('Data tidak ditemukan');
    }
    
    $existing_data = mysqli_fetch_assoc($check_result);
    
    // Cek apakah ada data anak yang masih aktif
    $children_query = "SELECT COUNT(*) as count FROM edoc_nomor WHERE PARENT_ID = $id AND STATUS = 1";
    $children_result = mysqli_query($koneksi, $children_query);
    $children_count = mysqli_fetch_assoc($children_result)['count'];
    
    if ($children_count > 0) {
        // Jika ada anak, hapus semua anak terlebih dahulu (cascade delete)
        $delete_children_query = "UPDATE edoc_nomor SET STATUS = 0, UPDATED_AT = NOW() WHERE PARENT_ID = $id";
        if (!mysqli_query($koneksi, $delete_children_query)) {
            throw new Exception('Gagal menghapus data anak: ' . mysqli_error($koneksi));
        }
        
        // Hapus cucu jika ada (untuk level 1 yang memiliki level 2 dan 3)
        $grandchildren_query = "UPDATE edoc_nomor SET STATUS = 0, UPDATED_AT = NOW() 
                               WHERE PARENT_ID IN (SELECT id FROM (SELECT id FROM edoc_nomor WHERE PARENT_ID = $id) as temp)";
        mysqli_query($koneksi, $grandchildren_query);
    }
    
    // Hapus data utama (soft delete)
    $delete_query = "UPDATE edoc_nomor SET STATUS = 0, UPDATED_AT = NOW() WHERE id = $id";
    
    if (!mysqli_query($koneksi, $delete_query)) {
        throw new Exception('Gagal menghapus data: ' . mysqli_error($koneksi));
    }
    
    if (mysqli_affected_rows($koneksi) == 0) {
        throw new Exception('Data tidak ditemukan atau sudah dihapus');
    }
    
    // Commit transaksi
    mysqli_commit($koneksi);
    mysqli_autocommit($koneksi, true);
    
    $message = 'Data "' . $existing_data['KODE'] . ' - ' . $existing_data['NAMA'] . '" berhasil dihapus';
    if ($children_count > 0) {
        $message .= ' beserta ' . $children_count . ' data anak';
    }
    
    echo json_encode([
        'success' => true, 
        'message' => $message
    ]);
    
} catch (Exception $e) {
    // Rollback transaksi
    mysqli_rollback($koneksi);
    mysqli_autocommit($koneksi, true);
    
    echo json_encode([
        'success' => false, 
        'message' => $e->getMessage()
    ]);
}
?>
