<?php
session_start();
include '../../config.php';
error_reporting(E_ALL ^ (E_NOTICE | E_WARNING));

// Cek session
if(empty($_SESSION['username'])){
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'Unauthorized']);
    exit;
}

$user = $_SESSION['username'];
$hak_akses = $_SESSION['hak_akses'];

// Hanya admin yang bisa akses
if($hak_akses != '1'){ 
    http_response_code(403);
    echo json_encode(['success' => false, 'message' => 'Forbidden']);
    exit;
}

// Validasi input
if (!isset($_POST['id']) || empty($_POST['id'])) {
    echo json_encode(['success' => false, 'message' => 'ID tidak valid']);
    exit;
}

$id = intval($_POST['id']);

// Query untuk mendapatkan detail data
$query = "SELECT id, KODE, NAMA, PARENT_ID, URUTAN, LEVEL FROM edoc_nomor WHERE id = $id AND STATUS = 1";
$result = mysqli_query($koneksi, $query);

if (!$result) {
    echo json_encode(['success' => false, 'message' => 'Query error: ' . mysqli_error($koneksi)]);
    exit;
}

if (mysqli_num_rows($result) == 0) {
    echo json_encode(['success' => false, 'message' => 'Data tidak ditemukan']);
    exit;
}

$data = mysqli_fetch_assoc($result);

echo json_encode([
    'success' => true,
    'data' => $data
]);
?>
