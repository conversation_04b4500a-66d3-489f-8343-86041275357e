-- =====================================================
-- Tabel edoc_nomor untuk Sistem Hierarki Nomor Dokumen
-- =====================================================

CREATE TABLE IF NOT EXISTS `edoc_nomor` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `KODE` varchar(50) NOT NULL COMMENT 'Kode hierarkis dokumen (AR, AR.01, AR.01.01)',
  `NAMA` varchar(255) NOT NULL COMMENT 'Nama dokumen/kategori',
  `PARENT_ID` int(11) DEFAULT NULL COMMENT 'ID parent untuk hierarki (NULL untuk level 1)',
  `URUTAN` int(11) NOT NULL DEFAULT 1 COMMENT 'Urutan relatif terhadap parent',
  `LEVEL` tinyint(1) NOT NULL DEFAULT 1 COMMENT 'Level hierarki (1=Parent, 2=Sub Parent, 3=Sub Sub Parent)',
  `STATUS` tinyint(1) NOT NULL DEFAULT 1 COMMENT 'Status aktif (1=aktif, 0=nonaktif)',
  `CREATED_BY` varchar(50) DEFAULT NULL COMMENT 'User yang membuat',
  `CREATED_AT` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `UPDATED_AT` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_kode` (`KODE`),
  KEY `idx_parent_id` (`PARENT_ID`),
  KEY `idx_level` (`LEVEL`),
  KEY `idx_status` (`STATUS`),
  CONSTRAINT `fk_edoc_nomor_parent` FOREIGN KEY (`PARENT_ID`) REFERENCES `edoc_nomor` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Tabel hierarki nomor dokumen 3 level';

-- =====================================================
-- Data Sample untuk Testing
-- =====================================================

INSERT INTO `edoc_nomor` (`KODE`, `NAMA`, `PARENT_ID`, `URUTAN`, `LEVEL`, `CREATED_BY`) VALUES
('AR', 'Kearsipan', NULL, 1, 1, 'admin'),
('BD', 'Bidang', NULL, 2, 1, 'admin'),
('KU', 'Keuangan', NULL, 3, 1, 'admin');

INSERT INTO `edoc_nomor` (`KODE`, `NAMA`, `PARENT_ID`, `URUTAN`, `LEVEL`, `CREATED_BY`) VALUES
('AR.01', 'Penciptaan Arsip', 1, 1, 2, 'admin'),
('AR.02', 'Pengelolaan Arsip', 1, 2, 2, 'admin'),
('BD.01', 'Bidang Medik', 2, 1, 2, 'admin'),
('BD.02', 'Bidang Keperawatan', 2, 2, 2, 'admin'),
('KU.01', 'Anggaran', 3, 1, 2, 'admin'),
('KU.02', 'Pembayaran', 3, 2, 2, 'admin');

INSERT INTO `edoc_nomor` (`KODE`, `NAMA`, `PARENT_ID`, `URUTAN`, `LEVEL`, `CREATED_BY`) VALUES
('AR.01.01', 'Pengelolaan Surat Masuk', 4, 1, 3, 'admin'),
('AR.01.02', 'Pengelolaan Surat Keluar', 4, 2, 3, 'admin'),
('AR.02.01', 'Penyimpanan Arsip', 5, 1, 3, 'admin'),
('BD.01.01', 'Pelayanan Medis', 6, 1, 3, 'admin'),
('BD.01.02', 'Tindakan Medis', 6, 2, 3, 'admin'),
('KU.01.01', 'Perencanaan Anggaran', 9, 1, 3, 'admin'),
('KU.01.02', 'Pelaksanaan Anggaran', 9, 2, 3, 'admin');

-- =====================================================
-- View untuk Menampilkan Hierarki Lengkap
-- =====================================================

CREATE OR REPLACE VIEW `v_edoc_nomor_hierarki` AS
SELECT 
    n.id,
    n.KODE,
    n.NAMA,
    n.PARENT_ID,
    CASE 
        WHEN n.PARENT_ID IS NULL THEN '-'
        ELSE p.NAMA 
    END AS PARENT_NAMA,
    n.URUTAN,
    n.LEVEL,
    n.STATUS,
    n.CREATED_BY,
    n.CREATED_AT,
    n.UPDATED_AT,
    -- Path hierarki lengkap
    CASE 
        WHEN n.LEVEL = 1 THEN n.NAMA
        WHEN n.LEVEL = 2 THEN CONCAT(p.NAMA, ' → ', n.NAMA)
        WHEN n.LEVEL = 3 THEN CONCAT(gp.NAMA, ' → ', p.NAMA, ' → ', n.NAMA)
        ELSE n.NAMA
    END AS HIERARKI_PATH
FROM edoc_nomor n
LEFT JOIN edoc_nomor p ON n.PARENT_ID = p.id
LEFT JOIN edoc_nomor gp ON p.PARENT_ID = gp.id
WHERE n.STATUS = 1
ORDER BY n.KODE;

-- =====================================================
-- Stored Procedure untuk Generate Kode Otomatis
-- =====================================================

DELIMITER $$

CREATE PROCEDURE `sp_generate_kode_nomor`(
    IN p_parent_id INT,
    IN p_level TINYINT,
    OUT p_kode_baru VARCHAR(50),
    OUT p_urutan_baru INT
)
BEGIN
    DECLARE v_parent_kode VARCHAR(50) DEFAULT '';
    DECLARE v_max_urutan INT DEFAULT 0;
    DECLARE v_kode_format VARCHAR(10);
    
    -- Jika level 1 (root), generate kode 2 huruf
    IF p_level = 1 THEN
        -- Cari urutan terakhir untuk level 1
        SELECT COALESCE(MAX(URUTAN), 0) INTO v_max_urutan 
        FROM edoc_nomor 
        WHERE PARENT_ID IS NULL AND STATUS = 1;
        
        SET p_urutan_baru = v_max_urutan + 1;
        
        -- Generate kode 2 huruf berdasarkan urutan
        SET v_kode_format = CASE p_urutan_baru
            WHEN 1 THEN 'AR'
            WHEN 2 THEN 'BD' 
            WHEN 3 THEN 'KU'
            WHEN 4 THEN 'AD'
            WHEN 5 THEN 'OP'
            WHEN 6 THEN 'TK'
            WHEN 7 THEN 'HU'
            WHEN 8 THEN 'KE'
            WHEN 9 THEN 'PE'
            WHEN 10 THEN 'PR'
            ELSE CONCAT('K', LPAD(p_urutan_baru, 2, '0'))
        END;
        
        SET p_kode_baru = v_kode_format;
        
    ELSE
        -- Untuk level 2 dan 3, ambil kode parent
        SELECT KODE INTO v_parent_kode 
        FROM edoc_nomor 
        WHERE id = p_parent_id AND STATUS = 1;
        
        -- Cari urutan terakhir untuk parent ini
        SELECT COALESCE(MAX(URUTAN), 0) INTO v_max_urutan 
        FROM edoc_nomor 
        WHERE PARENT_ID = p_parent_id AND STATUS = 1;
        
        SET p_urutan_baru = v_max_urutan + 1;
        
        -- Generate kode dengan format parent.XX
        SET p_kode_baru = CONCAT(v_parent_kode, '.', LPAD(p_urutan_baru, 2, '0'));
    END IF;
    
END$$

DELIMITER ;
