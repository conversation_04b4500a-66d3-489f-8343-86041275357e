<?php
/**
 * Script untuk testing Master <PERSON><PERSON> Do<PERSON>
 * Men<PERSON>ji semua fungsi CRUD dan logika kode otomatis
 */

include 'config.php';

echo "<h2>Testing Master Nomor Dokumen</h2>";
echo "<hr>";

// Test 1: <PERSON>k apakah tabel sudah ada
echo "<h3>Test 1: Cek Tabel Database</h3>";
$tables_to_check = ['edoc_nomor'];

foreach ($tables_to_check as $table) {
    $query = "SHOW TABLES LIKE '$table'";
    $result = mysqli_query($koneksi, $query);
    
    if (mysqli_num_rows($result) > 0) {
        echo "<p style='color: green;'>✓ Tabel '$table' ditemukan</p>";
    } else {
        echo "<p style='color: red;'>✗ Tabel '$table' tidak ditemukan</p>";
    }
}

// Test 2: Cek struktur tabel
echo "<h3>Test 2: Cek <PERSON>bel</h3>";
$query = "DESCRIBE edoc_nomor";
$result = mysqli_query($koneksi, $query);

if ($result) {
    echo "<table border='1' cellpadding='5' cellspacing='0'>";
    echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th></tr>";
    
    while ($row = mysqli_fetch_assoc($result)) {
        echo "<tr>";
        echo "<td>" . $row['Field'] . "</td>";
        echo "<td>" . $row['Type'] . "</td>";
        echo "<td>" . $row['Null'] . "</td>";
        echo "<td>" . $row['Key'] . "</td>";
        echo "<td>" . $row['Default'] . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    echo "<p style='color: green;'>✓ Struktur tabel OK</p>";
} else {
    echo "<p style='color: red;'>✗ Error: " . mysqli_error($koneksi) . "</p>";
}

// Test 3: Cek data sample
echo "<h3>Test 3: Cek Data Sample</h3>";
$query = "SELECT COUNT(*) as total FROM edoc_nomor WHERE STATUS = 1";
$result = mysqli_query($koneksi, $query);
$row = mysqli_fetch_assoc($result);

echo "<p>Total data aktif: " . $row['total'] . "</p>";

if ($row['total'] > 0) {
    echo "<p style='color: green;'>✓ Data sample tersedia</p>";
    
    // Tampilkan beberapa data sample
    $query = "SELECT KODE, NAMA, LEVEL FROM edoc_nomor WHERE STATUS = 1 ORDER BY KODE LIMIT 10";
    $result = mysqli_query($koneksi, $query);
    
    echo "<table border='1' cellpadding='5' cellspacing='0'>";
    echo "<tr><th>KODE</th><th>NAMA</th><th>LEVEL</th></tr>";
    
    while ($row = mysqli_fetch_assoc($result)) {
        echo "<tr>";
        echo "<td>" . $row['KODE'] . "</td>";
        echo "<td>" . $row['NAMA'] . "</td>";
        echo "<td>" . $row['LEVEL'] . "</td>";
        echo "</tr>";
    }
    echo "</table>";
} else {
    echo "<p style='color: orange;'>⚠ Tidak ada data sample</p>";
}

// Test 4: Cek stored procedure
echo "<h3>Test 4: Cek Stored Procedure</h3>";
$query = "SHOW PROCEDURE STATUS WHERE Name = 'sp_generate_kode_nomor'";
$result = mysqli_query($koneksi, $query);

if (mysqli_num_rows($result) > 0) {
    echo "<p style='color: green;'>✓ Stored procedure 'sp_generate_kode_nomor' ditemukan</p>";
    
    // Test stored procedure
    try {
        $stmt = mysqli_prepare($koneksi, "CALL sp_generate_kode_nomor(NULL, 1, @kode_baru, @urutan_baru)");
        if (mysqli_stmt_execute($stmt)) {
            mysqli_stmt_close($stmt);
            
            $result = mysqli_query($koneksi, "SELECT @kode_baru as kode_baru, @urutan_baru as urutan_baru");
            $row = mysqli_fetch_assoc($result);
            
            echo "<p>Test generate kode level 1: " . $row['kode_baru'] . " (urutan: " . $row['urutan_baru'] . ")</p>";
            echo "<p style='color: green;'>✓ Stored procedure berfungsi</p>";
        } else {
            echo "<p style='color: red;'>✗ Error menjalankan stored procedure</p>";
        }
    } catch (Exception $e) {
        echo "<p style='color: red;'>✗ Error: " . $e->getMessage() . "</p>";
    }
} else {
    echo "<p style='color: red;'>✗ Stored procedure 'sp_generate_kode_nomor' tidak ditemukan</p>";
}

// Test 5: Cek view
echo "<h3>Test 5: Cek View</h3>";
$query = "SHOW TABLES LIKE 'v_edoc_nomor_hierarki'";
$result = mysqli_query($koneksi, $query);

if (mysqli_num_rows($result) > 0) {
    echo "<p style='color: green;'>✓ View 'v_edoc_nomor_hierarki' ditemukan</p>";
    
    // Test view
    $query = "SELECT * FROM v_edoc_nomor_hierarki LIMIT 5";
    $result = mysqli_query($koneksi, $query);
    
    if ($result && mysqli_num_rows($result) > 0) {
        echo "<table border='1' cellpadding='5' cellspacing='0'>";
        echo "<tr><th>KODE</th><th>NAMA</th><th>PARENT_NAMA</th><th>HIERARKI_PATH</th></tr>";
        
        while ($row = mysqli_fetch_assoc($result)) {
            echo "<tr>";
            echo "<td>" . $row['KODE'] . "</td>";
            echo "<td>" . $row['NAMA'] . "</td>";
            echo "<td>" . $row['PARENT_NAMA'] . "</td>";
            echo "<td>" . $row['HIERARKI_PATH'] . "</td>";
            echo "</tr>";
        }
        echo "</table>";
        echo "<p style='color: green;'>✓ View berfungsi</p>";
    } else {
        echo "<p style='color: orange;'>⚠ View kosong atau error</p>";
    }
} else {
    echo "<p style='color: red;'>✗ View 'v_edoc_nomor_hierarki' tidak ditemukan</p>";
}

// Test 6: Cek file-file yang diperlukan
echo "<h3>Test 6: Cek File-file Sistem</h3>";
$files_to_check = [
    'main/master-nomor.php',
    'main/ajax/master_nomor_serverside.php',
    'main/ajax/master_nomor_get_parent.php',
    'main/ajax/master_nomor_get_children.php',
    'main/ajax/master_nomor_preview_kode.php',
    'main/ajax/master_nomor_create.php',
    'main/ajax/master_nomor_get_detail.php',
    'main/ajax/master_nomor_update.php',
    'main/ajax/master_nomor_delete.php'
];

foreach ($files_to_check as $file) {
    if (file_exists($file)) {
        echo "<p style='color: green;'>✓ File '$file' ditemukan</p>";
    } else {
        echo "<p style='color: red;'>✗ File '$file' tidak ditemukan</p>";
    }
}

// Hasil akhir
echo "<h3>Hasil Testing</h3>";
echo "<p><strong>Status:</strong> Testing selesai</p>";
echo "<p><strong>Aksi Selanjutnya:</strong></p>";
echo "<ul>";
echo "<li><a href='main/master-nomor.php'>Akses Master Nomor Dokumen</a></li>";
echo "<li><a href='main/index.php'>Kembali ke Dashboard</a></li>";
echo "</ul>";

?>

<!DOCTYPE html>
<html>
<head>
    <title>Testing Master Nomor Dokumen</title>
    <meta charset="utf-8">
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        h2, h3 { color: #333; }
        p { margin: 5px 0; }
        hr { margin: 15px 0; }
        table { border-collapse: collapse; margin: 10px 0; }
        th { background-color: #f8f9fa; }
        a { color: #007bff; text-decoration: none; }
        a:hover { text-decoration: underline; }
        ul { margin: 10px 0; }
        li { margin: 5px 0; }
    </style>
</head>
<body>
    <!-- Content sudah di-output di atas -->
</body>
</html>
